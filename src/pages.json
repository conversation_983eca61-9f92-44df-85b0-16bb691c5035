{
	// 组件自动引入规则
	"easycom": {
		// 是否开启自动扫描
		"autoscan": true,
		"custom": {
			// uni-ui 规则如下配置
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			// 自动导入组件: 以 Xtx 开头的组件, 在 components 文件夹中查找引入（需要重启服务器才可以生效）
			// 注意：路径 @ 表示 src , 后面加斜杠才能真正找到文件
			"^Xlam(.*)": "@/components/Xlam$1.vue"
		}
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		// 若依的登录
		{
			"path": "pages/login",
			"style": {
				"navigationBarTitleText": "登录"
			}
		}, {
			"path": "pages/register",
			"style": {
				"navigationBarTitleText": "注册"
			}
		}, {
			"path": "pages/index",
			"style": {
				"navigationBarTitleText": "若依移动端框架",
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/work/index",
			"style": {
				"navigationBarTitleText": "工作台"
			}
		}, {
			"path": "pages/mine/index",
			"style": {
				"navigationBarTitleText": "我的"
			}
		}, {
			"path": "pages/mine/avatar/index",
			"style": {
				"navigationBarTitleText": "修改头像"
			}
		}, {
			"path": "pages/mine/info/index",
			"style": {
				"navigationBarTitleText": "个人信息"
			}
		}, {
			"path": "pages/mine/info/edit",
			"style": {
				"navigationBarTitleText": "编辑资料"
			}
		}, {
			"path": "pages/mine/pwd/index",
			"style": {
				"navigationBarTitleText": "修改密码"
			}
		}, {
			"path": "pages/mine/setting/index",
			"style": {
				"navigationBarTitleText": "应用设置"
			}
		}, {
			"path": "pages/mine/help/index",
			"style": {
				"navigationBarTitleText": "常见问题"
			}
		}, {
			"path": "pages/mine/about/index",
			"style": {
				"navigationBarTitleText": "关于我们"
			}
		}, {
			"path": "pages/common/webview/index",
			"style": {
				"navigationBarTitleText": "浏览网页"
			}
		}, {
			"path": "pages/common/textview/index",
			"style": {
				"navigationBarTitleText": "浏览文本"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	// 设置 TabBar
	"tabBar": {
		"color": "#000000",
    "selectedColor": "#000000",
    "borderStyle": "white",
    "backgroundColor": "#ffffff",
		"list": [
			{
        "pagePath": "pages/index",
        "iconPath": "static/images/tabbar/home.png",
        "selectedIconPath": "static/images/tabbar/home_.png",
        "text": "首页"
      }, {
        "pagePath": "pages/work/index",
        "iconPath": "static/images/tabbar/work.png",
        "selectedIconPath": "static/images/tabbar/work_.png",
        "text": "工作台"
      }, {
        "pagePath": "pages/mine/index",
        "iconPath": "static/images/tabbar/mine.png",
        "selectedIconPath": "static/images/tabbar/mine_.png",
        "text": "我的"
      }
		]
	}
}